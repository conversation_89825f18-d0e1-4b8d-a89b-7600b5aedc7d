from colorama import Fore
import re, threading, json, random, time
from curl_cffi import requests
from bs4 import BeautifulSoup

config = json.load(open('./config.json'))
proxy = config['proxy']
lock = threading.Lock()

def s_print(content,color=Fore.WHITE):
  global lock
  lock.acquire()
  print(f"{color} > {Fore.WHITE}{content}")
  lock.release()
  
def extractNumb(number):
    clean_number = re.sub(r'\D', '', number)
    return clean_number

def read_txt(filename):
    with open(filename, "r", errors='ignore') as f:
        phones = f.readlines()
    return [phone.strip() for phone in phones]

def gen_session():
    spoofable = [
        "safari17_2_ios",
        "safari17_0",
        "safari_ios",
        "chrome124",
        "chrome123",
        "safari15_5"
    ]

    user_agents = [
        "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.3",
        "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.3",
        "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.6 Safari/605.1.1",
        "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.1 Safari/605.1.1",
        "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/129.0.0.",
        "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:130.0) Gecko/20100101 Firefox/130.",
        "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/125.0.0.0 Safari/537.36 Edg/125.0.0.",
        "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/128.0.0.0 Safari/537.36 Edg/128.0.0.",
        "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:109.0) Gecko/20100101 Firefox/117.",
        "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/109.0.0.0 Safari/537.3",
        "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/128.0.0.0 Safari/537.3",
        "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/124.0.0.0 Safari/537.36 Edg/124.0.0."
    ]

    session = requests.Session(impersonate=random.choice(spoofable))
    user_agent = random.choice(user_agents)

    return session, user_agent

def numerify(string: str) -> str:
    result = ''
    for char in string:
        if char in '1234567890':
            result += char
    return result 

def number_match_check(unconf_nums, conf_nums):
    full_conf_nums = set()

    if conf_nums is None:
        return []

    for conf_num in conf_nums:
        for unconf_num in unconf_nums:
            try:
                if 'XX' in unconf_num or "**" in unconf_num or '??' in unconf_num or 'None' in unconf_num or 'null' in unconf_num or 'Null' in unconf_num:
                    continue

                unconf_num = numerify(unconf_num)

                if not unconf_num.endswith(conf_num[-2:]):
                    continue

                if unconf_num.startswith('+44'):
                    unconf_num = unconf_num[3:]

                if unconf_num.startswith('1') and len(unconf_num) == 10:
                    unconf_num = unconf_num[1:]

                if unconf_num.startswith('+1'):
                    unconf_num = unconf_num[2:]

                full_conf_nums.add(unconf_num)
            except: pass

    return list(full_conf_nums)

def format_number(number):
    try:
        last_four_digits = number[-4:]
        return last_four_digits
    except: 
        return None